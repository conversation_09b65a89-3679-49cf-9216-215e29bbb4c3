{"version": 3, "sources": ["src/utils/tokenUtils.ts"], "sourcesContent": ["/**\n * Token 解析工具\n * 用于直接从 JWT Token 中解析信息，避免依赖异步状态更新\n */\n\n/**\n * 解析 JWT Token 的 payload\n * @param token JWT Token\n * @returns 解析后的 payload 对象\n */\nexport function parseJwtPayload(token: string): any {\n  try {\n    // JWT Token 格式：header.payload.signature\n    const parts = token.split('.');\n    if (parts.length !== 3) {\n      throw new Error('Invalid JWT format');\n    }\n\n    // 解码 payload (base64url)\n    const payload = parts[1];\n    // 处理 base64url 编码（替换 - 和 _ 字符，添加必要的 padding）\n    const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');\n    const paddedBase64 = base64 + '='.repeat((4 - (base64.length % 4)) % 4);\n\n    // 解码并解析 JSON\n    const decodedPayload = atob(paddedBase64);\n    return JSON.parse(decodedPayload);\n  } catch (error) {\n    console.error('Failed to parse JWT payload:', error);\n    return null;\n  }\n}\n\n/**\n * 从当前 Token 中获取团队 ID\n * @returns 团队 ID，如果没有团队信息则返回 null\n */\nexport function getTeamIdFromCurrentToken(): number | null {\n  try {\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return null;\n    }\n\n    const payload = parseJwtPayload(token);\n    if (!payload) {\n      return null;\n    }\n\n    return payload.teamId || null;\n  } catch (error) {\n    console.error('Failed to get team ID from token:', error);\n    return null;\n  }\n}\n\n/**\n * 从当前 Token 中获取用户 ID\n * @returns 用户 ID，如果没有用户信息则返回 null\n */\nexport function getUserIdFromCurrentToken(): number | null {\n  try {\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return null;\n    }\n\n    const payload = parseJwtPayload(token);\n    if (!payload) {\n      return null;\n    }\n\n    return payload.userId || null;\n  } catch (error) {\n    console.error('Failed to get user ID from token:', error);\n    return null;\n  }\n}\n\n/**\n * 检查当前 Token 是否包含团队信息\n * @returns 是否包含团队信息\n */\nexport function hasTeamInCurrentToken(): boolean {\n  const teamId = getTeamIdFromCurrentToken();\n  return teamId !== null && teamId !== undefined;\n}\n\n/**\n * 从当前 Token 中获取是否为创建者\n * @returns 是否为创建者，如果没有团队信息则返回 null\n */\nexport function getIsCreatorFromCurrentToken(): boolean | null {\n  try {\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return null;\n    }\n\n    const payload = parseJwtPayload(token);\n    if (!payload) {\n      return null;\n    }\n\n    return payload.isCreator || null;\n  } catch (error) {\n    console.error('Failed to get isCreator from token:', error);\n    return null;\n  }\n}\n\n/**\n * 获取当前 Token 的完整信息\n * @returns Token 中的所有信息\n */\nexport function getCurrentTokenInfo(): {\n  userId: number | null;\n  teamId: number | null;\n  isCreator: boolean | null;\n  email: string | null;\n  name: string | null;\n} {\n  try {\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return {\n        userId: null,\n        teamId: null,\n        isCreator: null,\n        email: null,\n        name: null,\n      };\n    }\n\n    const payload = parseJwtPayload(token);\n    if (!payload) {\n      return {\n        userId: null,\n        teamId: null,\n        isCreator: null,\n        email: null,\n        name: null,\n      };\n    }\n\n    return {\n      userId: payload.userId || null,\n      teamId: payload.teamId || null,\n      isCreator: payload.isCreator || null,\n      email: payload.email || null,\n      name: payload.name || null,\n    };\n  } catch (error) {\n    console.error('Failed to get token info:', error);\n    return {\n      userId: null,\n      teamId: null,\n      isCreator: null,\n      email: null,\n      name: null,\n    };\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;;CAGC,GAED;;;;CAIC;;;;;;;;;;;IA0Ge,mBAAmB;eAAnB;;IAvBA,4BAA4B;eAA5B;;IAvDA,yBAAyB;eAAzB;;IAuBA,yBAAyB;eAAzB;;IAuBA,qBAAqB;eAArB;;IAzEA,eAAe;eAAf;;;;;;;;;;;;;AAAT,SAAS,gBAAgB,KAAa;IAC3C,IAAI;QACF,wCAAwC;QACxC,MAAM,QAAQ,MAAM,KAAK,CAAC;QAC1B,IAAI,MAAM,MAAM,KAAK,GACnB,MAAM,IAAI,MAAM;QAGlB,yBAAyB;QACzB,MAAM,UAAU,KAAK,CAAC,EAAE;QACxB,6CAA6C;QAC7C,MAAM,SAAS,QAAQ,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;QACxD,MAAM,eAAe,SAAS,IAAI,MAAM,CAAC,AAAC,CAAA,IAAK,OAAO,MAAM,GAAG,CAAC,IAAK;QAErE,aAAa;QACb,MAAM,iBAAiB,KAAK;QAC5B,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAMO,SAAS;IACd,IAAI;QACF,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OACH,OAAO;QAGT,MAAM,UAAU,gBAAgB;QAChC,IAAI,CAAC,SACH,OAAO;QAGT,OAAO,QAAQ,MAAM,IAAI;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAMO,SAAS;IACd,IAAI;QACF,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OACH,OAAO;QAGT,MAAM,UAAU,gBAAgB;QAChC,IAAI,CAAC,SACH,OAAO;QAGT,OAAO,QAAQ,MAAM,IAAI;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAMO,SAAS;IACd,MAAM,SAAS;IACf,OAAO,WAAW,QAAQ,WAAW;AACvC;AAMO,SAAS;IACd,IAAI;QACF,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OACH,OAAO;QAGT,MAAM,UAAU,gBAAgB;QAChC,IAAI,CAAC,SACH,OAAO;QAGT,OAAO,QAAQ,SAAS,IAAI;IAC9B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAMO,SAAS;IAOd,IAAI;QACF,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OACH,OAAO;YACL,QAAQ;YACR,QAAQ;YACR,WAAW;YACX,OAAO;YACP,MAAM;QACR;QAGF,MAAM,UAAU,gBAAgB;QAChC,IAAI,CAAC,SACH,OAAO;YACL,QAAQ;YACR,QAAQ;YACR,WAAW;YACX,OAAO;YACP,MAAM;QACR;QAGF,OAAO;YACL,QAAQ,QAAQ,MAAM,IAAI;YAC1B,QAAQ,QAAQ,MAAM,IAAI;YAC1B,WAAW,QAAQ,SAAS,IAAI;YAChC,OAAO,QAAQ,KAAK,IAAI;YACxB,MAAM,QAAQ,IAAI,IAAI;QACxB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YACL,QAAQ;YACR,QAAQ;YACR,WAAW;YACX,OAAO;YACP,MAAM;QACR;IACF;AACF"}