{"version": 3, "sources": ["src/pages/team/detail/components/TeamDetailContent.tsx", "src/pages/team/detail/components/TeamMemberList.tsx", "src/pages/team/detail/index.tsx"], "sourcesContent": ["/**\n * 团队详情组件 - 增强模式显示\n */\n\nimport {\n  ArrowLeftOutlined,\n  CalendarOutlined,\n  ClockCircleOutlined,\n  CrownOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  ExclamationCircleOutlined,\n  SettingOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { history, useModel } from '@umijs/max';\nimport {\n  Avatar,\n  Badge,\n  Button,\n  Card,\n  Col,\n  Dropdown,\n  Empty,\n  Form,\n  Input,\n  Modal,\n  message,\n  Progress,\n  Row,\n  Space,\n  Spin,\n  Statistic,\n  Tag,\n  Typography,\n} from 'antd';\nimport React, { useState } from 'react';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\nimport TeamMemberList from './TeamMemberList';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamDetailContentProps {\n  teamDetail: TeamDetailResponse | null;\n  loading: boolean;\n  onRefresh: () => void;\n  /** 是否显示返回按钮 */\n  showBackButton?: boolean;\n  /** 返回按钮点击回调 */\n  onBack?: () => void;\n}\n\nconst TeamDetailContent: React.FC<TeamDetailContentProps> = ({\n  teamDetail,\n  loading,\n  onRefresh,\n  showBackButton = false,\n  onBack,\n}) => {\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [deleting, setDeleting] = useState(false);\n  const [form] = Form.useForm();\n  const { setInitialState } = useModel('@@initialState');\n\n  // 辅助函数\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    });\n  };\n\n  const getTeamStatusColor = () => {\n    if (!teamDetail) return '#1890ff';\n    const memberCount = teamDetail.memberCount;\n    if (memberCount >= 10) return '#52c41a'; // 绿色 - 活跃\n    if (memberCount >= 5) return '#faad14'; // 橙色 - 正常\n    return '#1890ff'; // 蓝色 - 小团队\n  };\n\n  const getTeamStatusText = () => {\n    if (!teamDetail) return '小型团队';\n    const memberCount = teamDetail.memberCount;\n    if (memberCount >= 10) return '活跃团队';\n    if (memberCount >= 5) return '正常团队';\n    return '小型团队';\n  };\n\n  const handleGoBack = () => {\n    if (onBack) {\n      onBack();\n    } else {\n      history.push('/user/team-select');\n    }\n  };\n\n  /**\n   * 处理编辑团队信息操作\n   */\n  const handleEdit = () => {\n    if (!teamDetail) return;\n    form.setFieldsValue({\n      name: teamDetail.name,\n      description: teamDetail.description || '',\n    });\n    setEditModalVisible(true);\n  };\n\n  /**\n   * 处理团队信息更新操作\n   */\n  const handleUpdateTeam = async (values: UpdateTeamRequest) => {\n    if (!teamDetail) return;\n\n    try {\n      setUpdating(true);\n      await TeamService.updateCurrentTeam(values);\n      message.success('团队信息更新成功');\n      setEditModalVisible(false);\n      onRefresh();\n    } catch (error) {\n      console.error('更新团队失败:', error);\n      message.error('更新团队失败');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  /**\n   * 处理删除团队操作\n   */\n  const handleDeleteTeam = () => {\n    if (!teamDetail) return;\n\n    Modal.confirm({\n      title: '确认删除团队',\n      content: `确定要删除团队 \"${teamDetail.name}\" 吗？此操作不可恢复。`,\n      icon: <ExclamationCircleOutlined />,\n      okText: '确认删除',\n      cancelText: '取消',\n      okType: 'danger',\n      onOk: async () => {\n        try {\n          setDeleting(true);\n          await TeamService.deleteCurrentTeam();\n          message.success('团队删除成功');\n          // 更新全局状态，清除当前团队\n          setInitialState((s) => ({ ...s, currentTeam: undefined }));\n          history.push('/user/team-select');\n        } catch (error) {\n          console.error('删除团队失败:', error);\n          message.error('删除团队失败');\n        } finally {\n          setDeleting(false);\n        }\n      },\n    });\n  };\n\n  // 创建下拉菜单项（增强模式使用）\n  const createMenuItems = () => [\n    {\n      key: 'edit',\n      icon: <EditOutlined />,\n      label: '编辑团队',\n      onClick: handleEdit,\n    },\n    {\n      key: 'delete',\n      icon: <DeleteOutlined />,\n      label: '删除团队',\n      danger: true,\n      onClick: handleDeleteTeam,\n    },\n  ];\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px 0' }}>\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <Empty\n        image={Empty.PRESENTED_IMAGE_SIMPLE}\n        description=\"请先选择一个团队\"\n      />\n    );\n  }\n\n  // 增强模式渲染\n  return (\n    <div style={{ padding: '0 24px' }}>\n      {/* 团队头部信息卡片 */}\n      <Card\n        style={{\n          marginBottom: 24,\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          border: 'none',\n          borderRadius: 16,\n        }}\n        styles={{ body: { padding: '32px' } }}\n      >\n        <Row align=\"middle\" justify=\"space-between\">\n          {/* 左列：返回按钮 */}\n          {showBackButton && (\n            <Col>\n              <Button\n                type=\"text\"\n                icon={<ArrowLeftOutlined />}\n                onClick={handleGoBack}\n                style={{\n                  color: 'rgba(255, 255, 255, 0.8)',\n                  fontSize: 16,\n                  padding: '4px 8px',\n                }}\n              >\n                返回\n              </Button>\n            </Col>\n          )}\n\n          {/* 中间列：团队名称显示 */}\n          <Col\n            flex=\"auto\"\n            style={{\n              display: 'flex',\n              justifyContent: 'center',\n              maxWidth: '60%',\n            }}\n          >\n            <Space size=\"large\" align=\"center\">\n              <Avatar\n                size={65}\n                icon={<TeamOutlined />}\n                style={{\n                  backgroundColor: 'rgba(255, 255, 255, 0.2)',\n                  color: 'white',\n                  fontSize: 28,\n                }}\n              />\n              <div>\n                <Space align=\"center\" style={{ marginBottom: 8 }}>\n                  <Title level={2} style={{ color: 'white', margin: 0 }}>\n                    {teamDetail.name}\n                  </Title>\n                  {teamDetail.isCreator && (\n                    <Tag\n                      icon={<CrownOutlined />}\n                      color=\"gold\"\n                      style={{ fontSize: 12 }}\n                    >\n                      管理员\n                    </Tag>\n                  )}\n                  <Badge\n                    color={getTeamStatusColor()}\n                    text={\n                      <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>\n                        {getTeamStatusText()}\n                      </Text>\n                    }\n                  />\n                </Space>\n                <Paragraph\n                  style={{\n                    color: 'rgba(255, 255, 255, 0.8)',\n                    margin: 0,\n                    textAlign: 'center',\n                  }}\n                  ellipsis={{ rows: 2 }}\n                >\n                  {teamDetail.description || '这个团队还没有描述'}\n                </Paragraph>\n              </div>\n            </Space>\n          </Col>\n\n          {/* 右列：团队操作菜单 */}\n          <Col>\n            {teamDetail.isCreator && (\n              <Dropdown\n                menu={{ items: createMenuItems() }}\n                trigger={['click']}\n                placement=\"bottomRight\"\n              >\n                <Button\n                  type=\"text\"\n                  icon={<SettingOutlined />}\n                  style={{\n                    color: 'rgba(255, 255, 255, 0.8)',\n                    fontSize: 20,\n                    width: 50,\n                    height: 50,\n                  }}\n                />\n              </Dropdown>\n            )}\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 团队统计信息 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"团队成员\"\n              value={teamDetail.memberCount}\n              suffix=\"人\"\n              prefix={<UserOutlined style={{ color: '#1890ff' }} />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"创建时间\"\n              value={formatDate(teamDetail.createdAt)}\n              prefix={<CalendarOutlined style={{ color: '#52c41a' }} />}\n              valueStyle={{ color: '#52c41a', fontSize: 16 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"最后活动\"\n              value={formatDate(teamDetail.updatedAt)}\n              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}\n              valueStyle={{ color: '#faad14', fontSize: 16 }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <div style={{ textAlign: 'center' }}>\n              <Text type=\"secondary\" style={{ fontSize: 14 }}>\n                团队活跃度\n              </Text>\n              <div style={{ marginTop: 8 }}>\n                <Progress\n                  type=\"circle\"\n                  size={60}\n                  percent={Math.min(teamDetail.memberCount * 10, 100)}\n                  strokeColor={getTeamStatusColor()}\n                  format={() => (\n                    <Text style={{ fontSize: 12, color: getTeamStatusColor() }}>\n                      {teamDetail.memberCount >= 10\n                        ? '高'\n                        : teamDetail.memberCount >= 5\n                          ? '中'\n                          : '低'}\n                    </Text>\n                  )}\n                />\n              </div>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 团队成员列表 */}\n      <TeamMemberList\n        teamId={teamDetail.id}\n        isCreator={teamDetail.isCreator}\n        onMemberChange={onRefresh}\n      />\n\n      {/* 编辑团队模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form form={form} layout=\"vertical\" onFinish={handleUpdateTeam}>\n          <Form.Item\n            label=\"团队名称\"\n            name=\"name\"\n            rules={[\n              { required: true, message: '请输入团队名称' },\n              { max: 50, message: '团队名称不能超过50个字符' },\n            ]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n          <Form.Item\n            label=\"团队描述\"\n            name=\"description\"\n            rules={[{ max: 200, message: '团队描述不能超过200个字符' }]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入团队描述（可选）\"\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => setEditModalVisible(false)}>取消</Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={updating}>\n                保存\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default TeamDetailContent;\n", "/**\n * 团队成员列表组件\n *\n * 功能特性：\n * - 展示团队所有成员信息（头像、姓名、邮箱、角色、状态等）\n * - 支持成员搜索和筛选功能\n * - 提供成员管理操作（移除成员、角色变更等）\n * - 区分创建者和普通成员的权限显示\n * - 响应式表格设计，适配不同屏幕尺寸\n *\n * 权限控制：\n * - 只有团队创建者可以看到管理操作按钮\n * - 创建者不能移除自己\n * - 普通成员只能查看成员列表\n *\n * 交互设计：\n * - 支持批量操作（预留功能）\n * - 提供详细的操作确认对话框\n * - 实时更新成员状态和数量\n */\n\nimport {\n  CheckCircleOutlined,\n  CrownOutlined,\n  DeleteOutlined,\n  ExclamationCircleOutlined,\n  FilterOutlined,\n  HarmonyOSOutlined,\n  MoreOutlined,\n  SearchOutlined,\n  StopOutlined,\n  UserOutlined,\n  UserSwitchOutlined,\n} from '@ant-design/icons';\nimport type { MenuProps } from 'antd';\nimport {\n  Avatar,\n  Badge,\n  Button,\n  Card,\n  Checkbox,\n  Divider,\n  Dropdown,\n  Input,\n  Modal,\n  message,\n  Select,\n  Space,\n  Table,\n  Tag,\n  Tooltip,\n  Typography,\n} from 'antd';\nimport type { ColumnsType } from 'antd/es/table';\nimport React, { useEffect, useState } from 'react';\nimport { TeamService } from '@/services';\nimport type { TeamMemberResponse } from '@/types/api';\n\nconst { Text } = Typography;\nconst { Option } = Select;\n\n/**\n * 团队成员列表组件的Props接口\n */\ninterface TeamMemberListProps {\n  /** 团队ID，用于获取成员列表 */\n  teamId: number;\n  /** 当前用户是否为团队创建者，控制管理功能的显示 */\n  isCreator: boolean;\n  /** 成员变更时的回调函数，用于通知父组件刷新数据 */\n  onMemberChange?: () => void;\n}\n\nconst TeamMemberList: React.FC<TeamMemberListProps> = ({\n  teamId,\n  isCreator,\n  onMemberChange,\n}) => {\n  const [loading, setLoading] = useState(true);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [filteredMembers, setFilteredMembers] = useState<TeamMemberResponse[]>(\n    [],\n  );\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [statusFilter, setStatusFilter] = useState<string>('all');\n\n  useEffect(() => {\n    fetchMembers();\n  }, [teamId]);\n\n  /**\n   * 成员列表过滤效果\n   *\n   * 过滤条件：\n   * 1. 搜索文本：匹配成员姓名或邮箱（不区分大小写）\n   * 2. 状态筛选：全部/活跃/非活跃/创建者/普通成员\n   *\n   * 安全性：\n   * - 添加空值检查，防止数据异常导致的错误\n   * - 确保成员对象的必要属性存在\n   */\n  useEffect(() => {\n    // 过滤成员列表 - 添加空值检查\n    if (!members || !Array.isArray(members)) {\n      setFilteredMembers([]);\n      return;\n    }\n\n    const filtered = members.filter((member) => {\n      // 确保member对象存在且有必要的属性\n      if (!member || !member.name || !member.email) {\n        return false;\n      }\n\n      // 搜索文本匹配（姓名或邮箱）\n      const matchesSearch =\n        !searchText ||\n        member.name.toLowerCase().includes(searchText.toLowerCase()) ||\n        member.email.toLowerCase().includes(searchText.toLowerCase());\n\n      // 状态筛选匹配\n      const matchesStatus =\n        statusFilter === 'all' ||\n        (statusFilter === 'active' && member.isActive) ||\n        (statusFilter === 'inactive' && !member.isActive) ||\n        (statusFilter === 'creator' && member.isCreator) ||\n        (statusFilter === 'member' && !member.isCreator);\n\n      return matchesSearch && matchesStatus;\n    });\n    setFilteredMembers(filtered);\n  }, [members, searchText, statusFilter]);\n\n  /**\n   * 获取团队成员列表\n   *\n   * 功能：\n   * - 调用API获取当前团队的所有成员\n   * - 设置加载状态，提供用户反馈\n   * - 处理错误情况，确保组件稳定性\n   *\n   * 数据处理：\n   * - 确保返回数据为数组格式，防止渲染错误\n   * - 错误时设置空数组，保持组件正常显示\n   */\n  const fetchMembers = async () => {\n    try {\n      setLoading(true);\n      const response = await TeamService.getTeamMembers({\n        current: 1,\n        pageSize: 1000,\n      });\n      // 确保返回的数据是数组格式，防止渲染错误\n      setMembers(response?.list || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n      // 出错时设置为空数组，保持组件正常显示\n      setMembers([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRemoveMember = (member: TeamMemberResponse) => {\n    if (member.isCreator) {\n      message.warning('不能移除团队创建者');\n      return;\n    }\n\n    Modal.confirm({\n      title: '确认移除成员',\n      content: `确定要移除成员 \"${member.name}\" 吗？`,\n      okText: '确认',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await TeamService.removeMember(member.id);\n          message.success('成员移除成功');\n          fetchMembers();\n          onMemberChange?.();\n        } catch (error) {\n          console.error('移除成员失败:', error);\n        }\n      },\n    });\n  };\n\n  const handleBatchRemove = () => {\n    const selectedMembers = members.filter(\n      (member) => selectedRowKeys.includes(member.id) && !member.isCreator,\n    );\n\n    if (selectedMembers.length === 0) {\n      message.warning('请选择要移除的成员');\n      return;\n    }\n\n    Modal.confirm({\n      title: '批量移除成员',\n      content: `确定要移除选中的 ${selectedMembers.length} 名成员吗？`,\n      okText: '确认',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await Promise.all(\n            selectedMembers.map((member) =>\n              TeamService.removeMember(member.id),\n            ),\n          );\n          message.success(`成功移除 ${selectedMembers.length} 名成员`);\n          setSelectedRowKeys([]);\n          fetchMembers();\n          onMemberChange?.();\n        } catch (error) {\n          console.error('批量移除成员失败:', error);\n          message.error('批量移除失败');\n        }\n      },\n    });\n  };\n\n  const columns: ColumnsType<TeamMemberResponse> = [\n    {\n      title: '成员',\n      dataIndex: 'name',\n      key: 'name',\n      render: (name, record) => (\n        <Space>\n          <Avatar size=\"small\" icon={<UserOutlined />} />\n          <div>\n            <div>{name}</div>\n            <div style={{ fontSize: 12, color: '#999' }}>{record.email}</div>\n          </div>\n        </Space>\n      ),\n    },\n    {\n      title: '角色',\n      dataIndex: 'isCreator',\n      key: 'role',\n      width: 100,\n      render: (isCreator) => (\n        <Tag\n          color={isCreator ? 'gold' : 'blue'}\n          icon={isCreator ? <CrownOutlined /> : <UserOutlined />}\n        >\n          {isCreator ? '创建者' : '成员'}\n        </Tag>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'status',\n      width: 80,\n      render: (isActive) => (\n        <Tag color={isActive ? 'green' : 'red'}>\n          {isActive ? '活跃' : '停用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '加入时间',\n      dataIndex: 'assignedAt',\n      key: 'assignedAt',\n      width: 150,\n      render: (assignedAt) => new Date(assignedAt).toLocaleDateString(),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      width: 150,\n      render: (lastAccessTime) => {\n        const date = new Date(lastAccessTime);\n        const now = new Date();\n        const diffDays = Math.floor(\n          (now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24),\n        );\n\n        let color = 'green';\n        if (diffDays > 7) color = 'orange';\n        if (diffDays > 30) color = 'red';\n\n        return (\n          <Tooltip title={date.toLocaleString()}>\n            <Tag color={color}>\n              {diffDays === 0 ? '今天' : `${diffDays}天前`}\n            </Tag>\n          </Tooltip>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      render: (_, record) => {\n        if (!isCreator || record.isCreator) {\n          return <Text type=\"secondary\">-</Text>;\n        }\n\n        const menuItems: MenuProps['items'] = [\n          {\n            key: 'remove',\n            label: '移除成员',\n            icon: <DeleteOutlined />,\n            danger: true,\n            onClick: () => handleRemoveMember(record),\n          },\n        ];\n\n        return (\n          <Space size=\"small\">\n            <Button\n              type=\"text\"\n              danger\n              size=\"small\"\n              icon={<DeleteOutlined />}\n              onClick={() => handleRemoveMember(record)}\n            >\n              移除\n            </Button>\n            <Dropdown menu={{ items: menuItems }} trigger={['click']}>\n              <Button type=\"text\" size=\"small\" icon={<HarmonyOSOutlined />} />\n            </Dropdown>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: (newSelectedRowKeys: React.Key[]) => {\n      setSelectedRowKeys(newSelectedRowKeys);\n    },\n    getCheckboxProps: (record: TeamMemberResponse) => ({\n      disabled: record.isCreator, // 创建者不能被选中\n    }),\n  };\n\n  return (\n    <Card\n      title={\n        <Space>\n          <Text strong>团队成员</Text>\n          <Badge count={filteredMembers.length} showZero />\n        </Space>\n      }\n      extra={\n        <Space>\n          <Select\n            value={statusFilter}\n            onChange={setStatusFilter}\n            style={{ width: 120 }}\n            size=\"small\"\n          >\n            <Option value=\"all\">全部</Option>\n            <Option value=\"active\">活跃</Option>\n            <Option value=\"inactive\">停用</Option>\n            <Option value=\"creator\">创建者</Option>\n            <Option value=\"member\">成员</Option>\n          </Select>\n          <Input\n            placeholder=\"搜索成员\"\n            prefix={<SearchOutlined />}\n            value={searchText}\n            onChange={(e) => setSearchText(e.target.value)}\n            style={{ width: 200 }}\n            size=\"small\"\n          />\n        </Space>\n      }\n    >\n      {selectedRowKeys.length > 0 && isCreator && (\n        <div\n          style={{\n            marginBottom: 16,\n            padding: 12,\n            background: '#f5f5f5',\n            borderRadius: 6,\n          }}\n        >\n          <Space>\n            <Text>已选择 {selectedRowKeys.length} 名成员</Text>\n            <Button\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={handleBatchRemove}\n            >\n              批量移除\n            </Button>\n            <Button size=\"small\" onClick={() => setSelectedRowKeys([])}>\n              取消选择\n            </Button>\n          </Space>\n        </div>\n      )}\n\n      <Table\n        columns={columns}\n        dataSource={filteredMembers}\n        rowKey=\"id\"\n        loading={loading}\n        rowSelection={isCreator ? rowSelection : undefined}\n        pagination={{\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `共 ${total} 名成员`,\n          pageSize: 10,\n        }}\n      />\n    </Card>\n  );\n};\n\nexport default TeamMemberList;\n", "/**\n * 团队详情页面\n */\n\nimport { PageContainer } from '@ant-design/pro-components';\nimport { message, Spin, Typography } from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { TeamService } from '@/services';\nimport type { TeamDetailResponse } from '@/types/api';\nimport TeamDetailContent from './components/TeamDetailContent';\n\nconst { Text } = Typography;\n\nconst TeamDetailPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);\n\n  useEffect(() => {\n    fetchTeamDetail();\n  }, []);\n\n  const fetchTeamDetail = async () => {\n    try {\n      setLoading(true);\n      const detail = await TeamService.getCurrentTeamDetail();\n      setTeamDetail(detail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n      message.error('获取团队详情失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Spin size=\"large\" />\n        </div>\n      </PageContainer>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Text type=\"secondary\">团队信息加载失败</Text>\n        </div>\n      </PageContainer>\n    );\n  }\n\n  return (\n    <PageContainer style={{ background: '#f5f5f5' }}>\n      <TeamDetailContent\n        teamDetail={teamDetail}\n        loading={loading}\n        onRefresh={fetchTeamDetail}\n        showBackButton={true}\n      />\n    </PageContainer>\n  );\n};\n\nexport default TeamDetailPage;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BAqaD;;;eAAA;;;;;;;8BAxZO;4BAC2B;6BAoB3B;wEACyB;iCACJ;gFAED;;;;;;;;;;AAE3B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;AAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAY1B,MAAM,oBAAsD,CAAC,EAC3D,UAAU,EACV,OAAO,EACP,SAAS,EACT,iBAAiB,KAAK,EACtB,MAAM,EACP;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAC3B,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IAErC,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,YAAY,OAAO;QACxB,MAAM,cAAc,WAAW,WAAW;QAC1C,IAAI,eAAe,IAAI,OAAO,WAAW,UAAU;QACnD,IAAI,eAAe,GAAG,OAAO,WAAW,UAAU;QAClD,OAAO,WAAW,WAAW;IAC/B;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY,OAAO;QACxB,MAAM,cAAc,WAAW,WAAW;QAC1C,IAAI,eAAe,IAAI,OAAO;QAC9B,IAAI,eAAe,GAAG,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,IAAI,QACF;aAEA,YAAO,CAAC,IAAI,CAAC;IAEjB;IAEA;;GAEC,GACD,MAAM,aAAa;QACjB,IAAI,CAAC,YAAY;QACjB,KAAK,cAAc,CAAC;YAClB,MAAM,WAAW,IAAI;YACrB,aAAa,WAAW,WAAW,IAAI;QACzC;QACA,oBAAoB;IACtB;IAEA;;GAEC,GACD,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,YAAY;QAEjB,IAAI;YACF,YAAY;YACZ,MAAM,qBAAW,CAAC,iBAAiB,CAAC;YACpC,aAAO,CAAC,OAAO,CAAC;YAChB,oBAAoB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB;QACvB,IAAI,CAAC,YAAY;QAEjB,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,SAAS,EAAE,WAAW,IAAI,CAAC,YAAY,CAAC;YAClD,oBAAM,2BAAC,gCAAyB;;;;;YAChC,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,MAAM;gBACJ,IAAI;oBACF,YAAY;oBACZ,MAAM,qBAAW,CAAC,iBAAiB;oBACnC,aAAO,CAAC,OAAO,CAAC;oBAChB,gBAAgB;oBAChB,gBAAgB,CAAC,IAAO,CAAA;4BAAE,GAAG,CAAC;4BAAE,aAAa;wBAAU,CAAA;oBACvD,YAAO,CAAC,IAAI,CAAC;gBACf,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;oBACzB,aAAO,CAAC,KAAK,CAAC;gBAChB,SAAU;oBACR,YAAY;gBACd;YACF;QACF;IACF;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,IAAM;YAC5B;gBACE,KAAK;gBACL,oBAAM,2BAAC,mBAAY;;;;;gBACnB,OAAO;gBACP,SAAS;YACX;YACA;gBACE,KAAK;gBACL,oBAAM,2BAAC,qBAAc;;;;;gBACrB,OAAO;gBACP,QAAQ;gBACR,SAAS;YACX;SACD;IAED,IAAI,SACF,qBACE,2BAAC;QAAI,OAAO;YAAE,WAAW;YAAU,SAAS;QAAS;kBACnD,cAAA,2BAAC,UAAI;YAAC,MAAK;;;;;;;;;;;IAKjB,IAAI,CAAC,YACH,qBACE,2BAAC,WAAK;QACJ,OAAO,WAAK,CAAC,sBAAsB;QACnC,aAAY;;;;;;IAKlB,SAAS;IACT,qBACE,2BAAC;QAAI,OAAO;YAAE,SAAS;QAAS;;0BAE9B,2BAAC,UAAI;gBACH,OAAO;oBACL,cAAc;oBACd,YAAY;oBACZ,QAAQ;oBACR,cAAc;gBAChB;gBACA,QAAQ;oBAAE,MAAM;wBAAE,SAAS;oBAAO;gBAAE;0BAEpC,cAAA,2BAAC,SAAG;oBAAC,OAAM;oBAAS,SAAQ;;wBAEzB,gCACC,2BAAC,SAAG;sCACF,cAAA,2BAAC,YAAM;gCACL,MAAK;gCACL,oBAAM,2BAAC,wBAAiB;;;;;gCACxB,SAAS;gCACT,OAAO;oCACL,OAAO;oCACP,UAAU;oCACV,SAAS;gCACX;0CACD;;;;;;;;;;;sCAOL,2BAAC,SAAG;4BACF,MAAK;4BACL,OAAO;gCACL,SAAS;gCACT,gBAAgB;gCAChB,UAAU;4BACZ;sCAEA,cAAA,2BAAC,WAAK;gCAAC,MAAK;gCAAQ,OAAM;;kDACxB,2BAAC,YAAM;wCACL,MAAM;wCACN,oBAAM,2BAAC,mBAAY;;;;;wCACnB,OAAO;4CACL,iBAAiB;4CACjB,OAAO;4CACP,UAAU;wCACZ;;;;;;kDAEF,2BAAC;;0DACC,2BAAC,WAAK;gDAAC,OAAM;gDAAS,OAAO;oDAAE,cAAc;gDAAE;;kEAC7C,2BAAC;wDAAM,OAAO;wDAAG,OAAO;4DAAE,OAAO;4DAAS,QAAQ;wDAAE;kEACjD,WAAW,IAAI;;;;;;oDAEjB,WAAW,SAAS,kBACnB,2BAAC,SAAG;wDACF,oBAAM,2BAAC,oBAAa;;;;;wDACpB,OAAM;wDACN,OAAO;4DAAE,UAAU;wDAAG;kEACvB;;;;;;kEAIH,2BAAC,WAAK;wDACJ,OAAO;wDACP,oBACE,2BAAC;4DAAK,OAAO;gEAAE,OAAO;4DAA2B;sEAC9C;;;;;;;;;;;;;;;;;0DAKT,2BAAC;gDACC,OAAO;oDACL,OAAO;oDACP,QAAQ;oDACR,WAAW;gDACb;gDACA,UAAU;oDAAE,MAAM;gDAAE;0DAEnB,WAAW,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;sCAOnC,2BAAC,SAAG;sCACD,WAAW,SAAS,kBACnB,2BAAC,cAAQ;gCACP,MAAM;oCAAE,OAAO;gCAAkB;gCACjC,SAAS;oCAAC;iCAAQ;gCAClB,WAAU;0CAEV,cAAA,2BAAC,YAAM;oCACL,MAAK;oCACL,oBAAM,2BAAC,sBAAe;;;;;oCACtB,OAAO;wCACL,OAAO;wCACP,UAAU;wCACV,OAAO;wCACP,QAAQ;oCACV;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASZ,2BAAC,SAAG;gBAAC,QAAQ;oBAAC;oBAAI;iBAAG;gBAAE,OAAO;oBAAE,cAAc;gBAAG;;kCAC/C,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW;gCAC7B,QAAO;gCACP,sBAAQ,2BAAC,mBAAY;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCAChD,YAAY;oCAAE,OAAO;gCAAU;;;;;;;;;;;;;;;;kCAIrC,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW,SAAS;gCACtC,sBAAQ,2BAAC,uBAAgB;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCACpD,YAAY;oCAAE,OAAO;oCAAW,UAAU;gCAAG;;;;;;;;;;;;;;;;kCAInD,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW,SAAS;gCACtC,sBAAQ,2BAAC,0BAAmB;oCAAC,OAAO;wCAAE,OAAO;oCAAU;;;;;;gCACvD,YAAY;oCAAE,OAAO;oCAAW,UAAU;gCAAG;;;;;;;;;;;;;;;;kCAInD,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAI,IAAI;kCACvB,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC;gCAAI,OAAO;oCAAE,WAAW;gCAAS;;kDAChC,2BAAC;wCAAK,MAAK;wCAAY,OAAO;4CAAE,UAAU;wCAAG;kDAAG;;;;;;kDAGhD,2BAAC;wCAAI,OAAO;4CAAE,WAAW;wCAAE;kDACzB,cAAA,2BAAC,cAAQ;4CACP,MAAK;4CACL,MAAM;4CACN,SAAS,KAAK,GAAG,CAAC,WAAW,WAAW,GAAG,IAAI;4CAC/C,aAAa;4CACb,QAAQ,kBACN,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,OAAO;oDAAqB;8DACtD,WAAW,WAAW,IAAI,KACvB,MACA,WAAW,WAAW,IAAI,IACxB,MACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWtB,2BAAC,uBAAc;gBACb,QAAQ,WAAW,EAAE;gBACrB,WAAW,WAAW,SAAS;gBAC/B,gBAAgB;;;;;;0BAIlB,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,oBAAoB;gBACpC,QAAQ;gBACR,OAAO;0BAEP,cAAA,2BAAC,UAAI;oBAAC,MAAM;oBAAM,QAAO;oBAAW,UAAU;;sCAC5C,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAU;gCACrC;oCAAE,KAAK;oCAAI,SAAS;gCAAgB;6BACrC;sCAED,cAAA,2BAAC,WAAK;gCAAC,aAAY;;;;;;;;;;;sCAErB,2BAAC,UAAI,CAAC,IAAI;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;gCAAC;oCAAE,KAAK;oCAAK,SAAS;gCAAiB;6BAAE;sCAEhD,cAAA,2BAAC;gCACC,MAAM;gCACN,aAAY;gCACZ,SAAS;gCACT,WAAW;;;;;;;;;;;sCAGf,2BAAC,UAAI,CAAC,IAAI;4BAAC,OAAO;gCAAE,cAAc;gCAAG,WAAW;4BAAQ;sCACtD,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,SAAS,IAAM,oBAAoB;kDAAQ;;;;;;kDACnD,2BAAC,YAAM;wCAAC,MAAK;wCAAU,UAAS;wCAAS,SAAS;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1E;GA9WM;;QAUW,UAAI,CAAC;QACQ,aAAQ;;;KAXhC;IAgXN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvaf;;;;;;;;;;;;;;;;;;;CAmBC;;;;4BAiZD;;;eAAA;;;;;;8BAnYO;6BAmBA;wEAEoC;iCACf;;;;;;;;;;AAG5B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,YAAM;AAczB,MAAM,iBAAgD,CAAC,EACrD,MAAM,EACN,SAAS,EACT,cAAc,EACf;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EACpD,EAAE;IAEJ,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAc,EAAE;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAS;IAEzD,IAAA,gBAAS,EAAC;QACR;IACF,GAAG;QAAC;KAAO;IAEX;;;;;;;;;;GAUC,GACD,IAAA,gBAAS,EAAC;QACR,kBAAkB;QAClB,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,UAAU;YACvC,mBAAmB,EAAE;YACrB;QACF;QAEA,MAAM,WAAW,QAAQ,MAAM,CAAC,CAAC;YAC/B,sBAAsB;YACtB,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,EAC1C,OAAO;YAGT,gBAAgB;YAChB,MAAM,gBACJ,CAAC,cACD,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAE5D,SAAS;YACT,MAAM,gBACJ,iBAAiB,SAChB,iBAAiB,YAAY,OAAO,QAAQ,IAC5C,iBAAiB,cAAc,CAAC,OAAO,QAAQ,IAC/C,iBAAiB,aAAa,OAAO,SAAS,IAC9C,iBAAiB,YAAY,CAAC,OAAO,SAAS;YAEjD,OAAO,iBAAiB;QAC1B;QACA,mBAAmB;IACrB,GAAG;QAAC;QAAS;QAAY;KAAa;IAEtC;;;;;;;;;;;GAWC,GACD,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qBAAW,CAAC,cAAc,CAAC;gBAChD,SAAS;gBACT,UAAU;YACZ;YACA,sBAAsB;YACtB,WAAW,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;YACd,qBAAqB;YACrB,WAAW,EAAE;QACf,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,OAAO,SAAS,EAAE;YACpB,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC;YACtC,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,IAAI;oBACF,MAAM,qBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;oBACxC,aAAO,CAAC,OAAO,CAAC;oBAChB;oBACA,2BAAA,6BAAA;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;gBAC3B;YACF;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,kBAAkB,QAAQ,MAAM,CACpC,CAAC,SAAW,gBAAgB,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,SAAS;QAGtE,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,aAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO;YACP,SAAS,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,MAAM,CAAC;YACnD,QAAQ;YACR,YAAY;YACZ,MAAM;gBACJ,IAAI;oBACF,MAAM,QAAQ,GAAG,CACf,gBAAgB,GAAG,CAAC,CAAC,SACnB,qBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;oBAGtC,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,gBAAgB,MAAM,CAAC,IAAI,CAAC;oBACpD,mBAAmB,EAAE;oBACrB;oBACA,2BAAA,6BAAA;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,aAAa;oBAC3B,aAAO,CAAC,KAAK,CAAC;gBAChB;YACF;QACF;IACF;IAEA,MAAM,UAA2C;QAC/C;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,MAAM,uBACb,2BAAC,WAAK;;sCACJ,2BAAC,YAAM;4BAAC,MAAK;4BAAQ,oBAAM,2BAAC,mBAAY;;;;;;;;;;sCACxC,2BAAC;;8CACC,2BAAC;8CAAK;;;;;;8CACN,2BAAC;oCAAI,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAO;8CAAI,OAAO,KAAK;;;;;;;;;;;;;;;;;;QAIlE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,0BACP,2BAAC,SAAG;oBACF,OAAO,YAAY,SAAS;oBAC5B,MAAM,0BAAY,2BAAC,oBAAa;;;;+CAAM,2BAAC,mBAAY;;;;;8BAElD,YAAY,QAAQ;;;;;;QAG3B;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,yBACP,2BAAC,SAAG;oBAAC,OAAO,WAAW,UAAU;8BAC9B,WAAW,OAAO;;;;;;QAGzB;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,aAAe,IAAI,KAAK,YAAY,kBAAkB;QACjE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC;gBACP,MAAM,OAAO,IAAI,KAAK;gBACtB,MAAM,MAAM,IAAI;gBAChB,MAAM,WAAW,KAAK,KAAK,CACzB,AAAC,CAAA,IAAI,OAAO,KAAK,KAAK,OAAO,EAAC,IAAM;gBAGtC,IAAI,QAAQ;gBACZ,IAAI,WAAW,GAAG,QAAQ;gBAC1B,IAAI,WAAW,IAAI,QAAQ;gBAE3B,qBACE,2BAAC,aAAO;oBAAC,OAAO,KAAK,cAAc;8BACjC,cAAA,2BAAC,SAAG;wBAAC,OAAO;kCACT,aAAa,IAAI,OAAO,CAAC,EAAE,SAAS,EAAE,CAAC;;;;;;;;;;;YAIhD;QACF;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG;gBACV,IAAI,CAAC,aAAa,OAAO,SAAS,EAChC,qBAAO,2BAAC;oBAAK,MAAK;8BAAY;;;;;;gBAGhC,MAAM,YAAgC;oBACpC;wBACE,KAAK;wBACL,OAAO;wBACP,oBAAM,2BAAC,qBAAc;;;;;wBACrB,QAAQ;wBACR,SAAS,IAAM,mBAAmB;oBACpC;iBACD;gBAED,qBACE,2BAAC,WAAK;oBAAC,MAAK;;sCACV,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAM;4BACN,MAAK;4BACL,oBAAM,2BAAC,qBAAc;;;;;4BACrB,SAAS,IAAM,mBAAmB;sCACnC;;;;;;sCAGD,2BAAC,cAAQ;4BAAC,MAAM;gCAAE,OAAO;4BAAU;4BAAG,SAAS;gCAAC;6BAAQ;sCACtD,cAAA,2BAAC,YAAM;gCAAC,MAAK;gCAAO,MAAK;gCAAQ,oBAAM,2BAAC,wBAAiB;;;;;;;;;;;;;;;;;;;;;YAIjE;QACF;KACD;IAED,MAAM,eAAe;QACnB;QACA,UAAU,CAAC;YACT,mBAAmB;QACrB;QACA,kBAAkB,CAAC,SAAgC,CAAA;gBACjD,UAAU,OAAO,SAAS;YAC5B,CAAA;IACF;IAEA,qBACE,2BAAC,UAAI;QACH,qBACE,2BAAC,WAAK;;8BACJ,2BAAC;oBAAK,MAAM;8BAAC;;;;;;8BACb,2BAAC,WAAK;oBAAC,OAAO,gBAAgB,MAAM;oBAAE,QAAQ;;;;;;;;;;;;QAGlD,qBACE,2BAAC,WAAK;;8BACJ,2BAAC,YAAM;oBACL,OAAO;oBACP,UAAU;oBACV,OAAO;wBAAE,OAAO;oBAAI;oBACpB,MAAK;;sCAEL,2BAAC;4BAAO,OAAM;sCAAM;;;;;;sCACpB,2BAAC;4BAAO,OAAM;sCAAS;;;;;;sCACvB,2BAAC;4BAAO,OAAM;sCAAW;;;;;;sCACzB,2BAAC;4BAAO,OAAM;sCAAU;;;;;;sCACxB,2BAAC;4BAAO,OAAM;sCAAS;;;;;;;;;;;;8BAEzB,2BAAC,WAAK;oBACJ,aAAY;oBACZ,sBAAQ,2BAAC,qBAAc;;;;;oBACvB,OAAO;oBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oBAC7C,OAAO;wBAAE,OAAO;oBAAI;oBACpB,MAAK;;;;;;;;;;;;;YAKV,gBAAgB,MAAM,GAAG,KAAK,2BAC7B,2BAAC;gBACC,OAAO;oBACL,cAAc;oBACd,SAAS;oBACT,YAAY;oBACZ,cAAc;gBAChB;0BAEA,cAAA,2BAAC,WAAK;;sCACJ,2BAAC;;gCAAK;gCAAK,gBAAgB,MAAM;gCAAC;;;;;;;sCAClC,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAM;4BACN,oBAAM,2BAAC,qBAAc;;;;;4BACrB,SAAS;sCACV;;;;;;sCAGD,2BAAC,YAAM;4BAAC,MAAK;4BAAQ,SAAS,IAAM,mBAAmB,EAAE;sCAAG;;;;;;;;;;;;;;;;;0BAOlE,2BAAC,WAAK;gBACJ,SAAS;gBACT,YAAY;gBACZ,QAAO;gBACP,SAAS;gBACT,cAAc,YAAY,eAAe;gBACzC,YAAY;oBACV,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;oBACtC,UAAU;gBACZ;;;;;;;;;;;;AAIR;GAzVM;KAAA;IA2VN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpaf;;CAEC;;;;4BAgED;;;eAAA;;;;;;;sCA9D8B;6BACY;wEACC;iCACf;mFAEE;;;;;;;;;;AAE9B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAE3B,MAAM,iBAA2B;;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAA4B;IAExE,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,MAAM,qBAAW,CAAC,oBAAoB;YACrD,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SACF,qBACE,2BAAC,4BAAa;kBACZ,cAAA,2BAAC;YAAI,OAAO;gBAAE,WAAW;gBAAU,SAAS;YAAS;sBACnD,cAAA,2BAAC,UAAI;gBAAC,MAAK;;;;;;;;;;;;;;;;IAMnB,IAAI,CAAC,YACH,qBACE,2BAAC,4BAAa;kBACZ,cAAA,2BAAC;YAAI,OAAO;gBAAE,WAAW;gBAAU,SAAS;YAAS;sBACnD,cAAA,2BAAC;gBAAK,MAAK;0BAAY;;;;;;;;;;;;;;;;IAM/B,qBACE,2BAAC,4BAAa;QAAC,OAAO;YAAE,YAAY;QAAU;kBAC5C,cAAA,2BAAC,0BAAiB;YAChB,YAAY;YACZ,SAAS;YACT,WAAW;YACX,gBAAgB;;;;;;;;;;;AAIxB;GAnDM;KAAA;IAqDN,WAAe"}