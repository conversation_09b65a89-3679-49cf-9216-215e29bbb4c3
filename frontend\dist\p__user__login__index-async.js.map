{"version": 3, "sources": ["config/defaultSettings.ts", "src/pages/user/login/index.tsx"], "sourcesContent": ["import type { ProLayoutProps } from '@ant-design/pro-components';\n\nconst Settings: ProLayoutProps & {\n  pwa?: boolean;\n  logo?: string;\n} = {\n  navTheme: 'light',\n  colorPrimary: '#1890ff',\n  layout: 'side',\n  contentWidth: 'Fluid',\n  fixedHeader: false,\n  fixSiderbar: true,\n  colorWeak: false,\n  title: '团队协作管理系统',\n  pwa: false,\n  logo: '/logo.svg',\n  iconfontUrl: '',\n  token: {},\n};\n\nexport default Settings;\n", "/**\n * 登录页面\n * 实现双阶段认证的第一阶段：账号登录\n */\n\nimport { LockOutlined, MailOutlined, UserOutlined } from '@ant-design/icons';\nimport { Helmet, history, useModel } from '@umijs/max';\nimport {\n  Button,\n  Card,\n  Form,\n  Input,\n  message,\n  Space,\n  Tabs,\n  Typography,\n} from 'antd';\nimport { createStyles } from 'antd-style';\nimport React, { useState } from 'react';\nimport { Footer } from '@/components';\nimport { AuthService } from '@/services';\nimport type { LoginRequest, RegisterRequest } from '@/types/api';\nimport Settings from '../../../../config/defaultSettings';\n\nconst { Title, Text } = Typography;\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundImage:\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\n      backgroundSize: '100% 100%',\n    },\n    content: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: '32px 16px',\n    },\n    header: {\n      marginBottom: 40,\n      textAlign: 'center',\n    },\n    logo: {\n      marginBottom: 16,\n    },\n    title: {\n      marginBottom: 0,\n    },\n    loginCard: {\n      width: '100%',\n      maxWidth: 400,\n      boxShadow: token.boxShadowTertiary,\n    },\n    footer: {\n      marginTop: 40,\n      textAlign: 'center',\n    },\n    lang: {\n      width: 42,\n      height: 42,\n      lineHeight: '42px',\n      position: 'fixed',\n      right: 16,\n      top: 16,\n      borderRadius: token.borderRadius,\n      ':hover': {\n        backgroundColor: token.colorBgTextHover,\n      },\n    },\n  };\n});\n\nconst LoginPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState('login');\n  const { styles } = useStyles();\n  const { setInitialState } = useModel('@@initialState');\n\n  // 自定义邮箱验证函数\n  const validateEmail = (email: string) => {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  };\n\n  // 处理登录\n  const handleLogin = async (values: LoginRequest) => {\n    setLoading(true);\n    try {\n      const response = await AuthService.login(values);\n      message.success('登录成功！');\n\n      // 登录成功后，刷新 initialState\n      await setInitialState((prevState) => ({\n        ...prevState,\n        currentUser: response.user,\n        currentTeam: response.teams.length > 0 ? response.teams[0] : undefined,\n      }));\n\n      // 根据团队数量进行不同的跳转处理\n      if (response.teams.length === 0) {\n        // 没有团队，跳转到创建团队页面\n        history.push('/team/create');\n      } else {\n        // 有团队（无论一个还是多个），都跳转到个人中心整合页面\n        history.push('/personal-center', { teams: response.teams });\n      }\n    } catch (error) {\n      console.error('登录失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 处理注册\n  const handleRegister = async (values: RegisterRequest) => {\n    setLoading(true);\n    try {\n      await AuthService.register(values);\n      message.success('注册成功！请登录');\n      setActiveTab('login');\n    } catch (error) {\n      console.error('注册失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 登录表单\n  const LoginForm = () => (\n    <Form name=\"login\" size=\"large\" onFinish={handleLogin} autoComplete=\"off\">\n      <Form.Item\n        name=\"email\"\n        rules={[\n          { required: true, message: '请输入邮箱！' },\n          {\n            validator: (_, value) => {\n              if (!value || validateEmail(value)) {\n                return Promise.resolve();\n              }\n              return Promise.reject(new Error('请输入有效的邮箱地址！'));\n            },\n          },\n        ]}\n      >\n        <Input\n          prefix={<MailOutlined />}\n          placeholder=\"邮箱\"\n          autoComplete=\"email\"\n        />\n      </Form.Item>\n\n      <Form.Item\n        name=\"password\"\n        rules={[{ required: true, message: '请输入密码！' }]}\n      >\n        <Input.Password\n          prefix={<LockOutlined />}\n          placeholder=\"密码\"\n          autoComplete=\"current-password\"\n        />\n      </Form.Item>\n\n      <Form.Item>\n        <Button type=\"primary\" htmlType=\"submit\" loading={loading} block>\n          登录\n        </Button>\n      </Form.Item>\n    </Form>\n  );\n\n  // 注册表单\n  const RegisterForm = () => (\n    <Form\n      name=\"register\"\n      size=\"large\"\n      onFinish={handleRegister}\n      autoComplete=\"off\"\n    >\n      <Form.Item\n        name=\"name\"\n        rules={[\n          { required: true, message: '请输入用户名！' },\n          { max: 100, message: '用户名长度不能超过100字符！' },\n        ]}\n      >\n        <Input\n          prefix={<UserOutlined />}\n          placeholder=\"用户名\"\n          autoComplete=\"name\"\n        />\n      </Form.Item>\n\n      <Form.Item\n        name=\"email\"\n        rules={[\n          { required: true, message: '请输入邮箱！' },\n          {\n            validator: (_, value) => {\n              if (!value || validateEmail(value)) {\n                return Promise.resolve();\n              }\n              return Promise.reject(new Error('请输入有效的邮箱地址！'));\n            },\n          },\n        ]}\n      >\n        <Input\n          prefix={<MailOutlined />}\n          placeholder=\"邮箱\"\n          autoComplete=\"email\"\n        />\n      </Form.Item>\n\n      <Form.Item\n        name=\"password\"\n        rules={[\n          { required: true, message: '请输入密码！' },\n          { min: 8, message: '密码长度至少8位！' },\n        ]}\n      >\n        <Input.Password\n          prefix={<LockOutlined />}\n          placeholder=\"密码（至少8位）\"\n          autoComplete=\"new-password\"\n        />\n      </Form.Item>\n\n      <Form.Item\n        name=\"confirmPassword\"\n        dependencies={['password']}\n        rules={[\n          { required: true, message: '请确认密码！' },\n          ({ getFieldValue }) => ({\n            validator(_, value) {\n              if (!value || getFieldValue('password') === value) {\n                return Promise.resolve();\n              }\n              return Promise.reject(new Error('两次输入的密码不一致！'));\n            },\n          }),\n        ]}\n      >\n        <Input.Password\n          prefix={<LockOutlined />}\n          placeholder=\"确认密码\"\n          autoComplete=\"new-password\"\n        />\n      </Form.Item>\n\n      <Form.Item>\n        <Button type=\"primary\" htmlType=\"submit\" loading={loading} block>\n          注册\n        </Button>\n      </Form.Item>\n    </Form>\n  );\n\n  const tabItems = [\n    {\n      key: 'login',\n      label: '登录',\n      children: <LoginForm />,\n    },\n    {\n      key: 'register',\n      label: '注册',\n      children: <RegisterForm />,\n    },\n  ];\n\n  return (\n    <div className={styles.container}>\n      <Helmet>\n        <title>\n          登录页\n          {Settings.title && ` - ${Settings.title}`}\n        </title>\n      </Helmet>\n      <div className={styles.content}>\n        <div className={styles.header}>\n          <Space direction=\"vertical\" align=\"center\" size=\"large\">\n            <div className={styles.logo}>\n              <img src=\"/logo.svg\" alt=\"TeamAuth\" height={48} />\n            </div>\n            <div className={styles.title}>\n              <Title level={2}>团队管理系统</Title>\n              <Text type=\"secondary\">现代化的团队协作与管理平台</Text>\n            </div>\n          </Space>\n        </div>\n\n        <Card className={styles.loginCard}>\n          <Tabs\n            activeKey={activeTab}\n            onChange={setActiveTab}\n            centered\n            items={tabItems}\n          />\n        </Card>\n\n        <div className={styles.footer}>\n          <Text type=\"secondary\">© 2025 TeamAuth. All rights reserved.</Text>\n        </div>\n      </div>\n      <Footer />\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "names": [], "mappings": ";;;;;;;4BAoBA;;;eAAA;;;;;;;;;;;;;AAlBA,MAAM,WAGF;IACF,UAAU;IACV,cAAc;IACd,QAAQ;IACR,cAAc;IACd,aAAa;IACb,aAAa;IACb,WAAW;IACX,OAAO;IACP,KAAK;IACL,MAAM;IACN,aAAa;IACb,OAAO,CAAC;AACV;IAEA,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;ACpBf;;;CAGC;;;;4BAyTD;;;eAAA;;;;;;;8BAvTyD;4BACf;6BAUnC;kCACsB;wEACG;mCACT;iCACK;iFAEP;;;;;;;;;;AAErB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC,MAAM,YAAY,IAAA,uBAAY,EAAC,CAAC,EAAE,KAAK,EAAE;IACvC,OAAO;QACL,WAAW;YACT,SAAS;YACT,eAAe;YACf,QAAQ;YACR,UAAU;YACV,iBACE;YACF,gBAAgB;QAClB;QACA,SAAS;YACP,MAAM;YACN,SAAS;YACT,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,SAAS;QACX;QACA,QAAQ;YACN,cAAc;YACd,WAAW;QACb;QACA,MAAM;YACJ,cAAc;QAChB;QACA,OAAO;YACL,cAAc;QAChB;QACA,WAAW;YACT,OAAO;YACP,UAAU;YACV,WAAW,MAAM,iBAAiB;QACpC;QACA,QAAQ;YACN,WAAW;YACX,WAAW;QACb;QACA,MAAM;YACJ,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,UAAU;YACV,OAAO;YACP,KAAK;YACL,cAAc,MAAM,YAAY;YAChC,UAAU;gBACR,iBAAiB,MAAM,gBAAgB;YACzC;QACF;IACF;AACF;AAEA,MAAM,YAAsB;;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAC3C,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IAErC,YAAY;IACZ,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA,OAAO;IACP,MAAM,cAAc,OAAO;QACzB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,qBAAW,CAAC,KAAK,CAAC;YACzC,aAAO,CAAC,OAAO,CAAC;YAEhB,wBAAwB;YACxB,MAAM,gBAAgB,CAAC,YAAe,CAAA;oBACpC,GAAG,SAAS;oBACZ,aAAa,SAAS,IAAI;oBAC1B,aAAa,SAAS,KAAK,CAAC,MAAM,GAAG,IAAI,SAAS,KAAK,CAAC,EAAE,GAAG;gBAC/D,CAAA;YAEA,kBAAkB;YAClB,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,GAC5B,iBAAiB;YACjB,YAAO,CAAC,IAAI,CAAC;iBAEb,6BAA6B;YAC7B,YAAO,CAAC,IAAI,CAAC,oBAAoB;gBAAE,OAAO,SAAS,KAAK;YAAC;QAE7D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;IACP,MAAM,iBAAiB,OAAO;QAC5B,WAAW;QACX,IAAI;YACF,MAAM,qBAAW,CAAC,QAAQ,CAAC;YAC3B,aAAO,CAAC,OAAO,CAAC;YAChB,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;IACP,MAAM,YAAY,kBAChB,2BAAC,UAAI;YAAC,MAAK;YAAQ,MAAK;YAAQ,UAAU;YAAa,cAAa;;8BAClE,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAO;wBACL;4BAAE,UAAU;4BAAM,SAAS;wBAAS;wBACpC;4BACE,WAAW,CAAC,GAAG;gCACb,IAAI,CAAC,SAAS,cAAc,QAC1B,OAAO,QAAQ,OAAO;gCAExB,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;4BAClC;wBACF;qBACD;8BAED,cAAA,2BAAC,WAAK;wBACJ,sBAAQ,2BAAC,mBAAY;;;;;wBACrB,aAAY;wBACZ,cAAa;;;;;;;;;;;8BAIjB,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAO;wBAAC;4BAAE,UAAU;4BAAM,SAAS;wBAAS;qBAAE;8BAE9C,cAAA,2BAAC,WAAK,CAAC,QAAQ;wBACb,sBAAQ,2BAAC,mBAAY;;;;;wBACrB,aAAY;wBACZ,cAAa;;;;;;;;;;;8BAIjB,2BAAC,UAAI,CAAC,IAAI;8BACR,cAAA,2BAAC,YAAM;wBAAC,MAAK;wBAAU,UAAS;wBAAS,SAAS;wBAAS,KAAK;kCAAC;;;;;;;;;;;;;;;;;IAOvE,OAAO;IACP,MAAM,eAAe,kBACnB,2BAAC,UAAI;YACH,MAAK;YACL,MAAK;YACL,UAAU;YACV,cAAa;;8BAEb,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAO;wBACL;4BAAE,UAAU;4BAAM,SAAS;wBAAU;wBACrC;4BAAE,KAAK;4BAAK,SAAS;wBAAkB;qBACxC;8BAED,cAAA,2BAAC,WAAK;wBACJ,sBAAQ,2BAAC,mBAAY;;;;;wBACrB,aAAY;wBACZ,cAAa;;;;;;;;;;;8BAIjB,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAO;wBACL;4BAAE,UAAU;4BAAM,SAAS;wBAAS;wBACpC;4BACE,WAAW,CAAC,GAAG;gCACb,IAAI,CAAC,SAAS,cAAc,QAC1B,OAAO,QAAQ,OAAO;gCAExB,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;4BAClC;wBACF;qBACD;8BAED,cAAA,2BAAC,WAAK;wBACJ,sBAAQ,2BAAC,mBAAY;;;;;wBACrB,aAAY;wBACZ,cAAa;;;;;;;;;;;8BAIjB,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAO;wBACL;4BAAE,UAAU;4BAAM,SAAS;wBAAS;wBACpC;4BAAE,KAAK;4BAAG,SAAS;wBAAY;qBAChC;8BAED,cAAA,2BAAC,WAAK,CAAC,QAAQ;wBACb,sBAAQ,2BAAC,mBAAY;;;;;wBACrB,aAAY;wBACZ,cAAa;;;;;;;;;;;8BAIjB,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,cAAc;wBAAC;qBAAW;oBAC1B,OAAO;wBACL;4BAAE,UAAU;4BAAM,SAAS;wBAAS;wBACpC,CAAC,EAAE,aAAa,EAAE,GAAM,CAAA;gCACtB,WAAU,CAAC,EAAE,KAAK;oCAChB,IAAI,CAAC,SAAS,cAAc,gBAAgB,OAC1C,OAAO,QAAQ,OAAO;oCAExB,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;gCAClC;4BACF,CAAA;qBACD;8BAED,cAAA,2BAAC,WAAK,CAAC,QAAQ;wBACb,sBAAQ,2BAAC,mBAAY;;;;;wBACrB,aAAY;wBACZ,cAAa;;;;;;;;;;;8BAIjB,2BAAC,UAAI,CAAC,IAAI;8BACR,cAAA,2BAAC,YAAM;wBAAC,MAAK;wBAAU,UAAS;wBAAS,SAAS;wBAAS,KAAK;kCAAC;;;;;;;;;;;;;;;;;IAOvE,MAAM,WAAW;QACf;YACE,KAAK;YACL,OAAO;YACP,wBAAU,2BAAC;;;;;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,wBAAU,2BAAC;;;;;QACb;KACD;IAED,qBACE,2BAAC;QAAI,WAAW,OAAO,SAAS;;0BAC9B,2BAAC,WAAM;0BACL,cAAA,2BAAC;;wBAAM;wBAEJ,wBAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,wBAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;0BAG7C,2BAAC;gBAAI,WAAW,OAAO,OAAO;;kCAC5B,2BAAC;wBAAI,WAAW,OAAO,MAAM;kCAC3B,cAAA,2BAAC,WAAK;4BAAC,WAAU;4BAAW,OAAM;4BAAS,MAAK;;8CAC9C,2BAAC;oCAAI,WAAW,OAAO,IAAI;8CACzB,cAAA,2BAAC;wCAAI,KAAI;wCAAY,KAAI;wCAAW,QAAQ;;;;;;;;;;;8CAE9C,2BAAC;oCAAI,WAAW,OAAO,KAAK;;sDAC1B,2BAAC;4CAAM,OAAO;sDAAG;;;;;;sDACjB,2BAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;;;;;;kCAK7B,2BAAC,UAAI;wBAAC,WAAW,OAAO,SAAS;kCAC/B,cAAA,2BAAC,UAAI;4BACH,WAAW;4BACX,UAAU;4BACV,QAAQ;4BACR,OAAO;;;;;;;;;;;kCAIX,2BAAC;wBAAI,WAAW,OAAO,MAAM;kCAC3B,cAAA,2BAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;;;;;;;;0BAG3B,2BAAC,kBAAM;;;;;;;;;;;AAGb;GA3OM;;QAGe;QACS,aAAQ;;;KAJhC;IA6ON,WAAe"}